export interface Member {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company?: string; // Nom de l'entreprise
  memberType: MemberType;
  subscriptionType: string;
  status: MemberStatus;
  studentCode?: string; // Uniquement pour les étudiants
  iceNumber?: string; // Uniquement pour les entreprises
  createdAt: Date;
  updatedAt: Date;
  profileImage?: string;


}

export enum MemberType {
  STUDENT = 'STUDENT',
  PROFESSIONAL = 'PROFESSIONAL',
  COMPANY = 'COMPANY'
}

export enum MemberStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

export interface MemberHistory {
  id: string;
  memberId: string;
  type: HistoryType;
  description: string;
  amount?: number;
  date: Date;
  details?: any;
}

export enum HistoryType {
  RESERVATION = 'reservation',
  PAYMENT = 'payment',
  SUBSCRIPTION_CHANGE = 'subscription_change',
  STATUS_CHANGE = 'status_change'
}

export interface Subscription {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  duration: number; // en jours
  features: string[];
  maxReservationsPerDay: number;
  maxReservationsPerWeek: number;
  maxReservationsPerMonth: number;
  maxConsecutiveHours: number;
  advanceBookingDays: number;
  canBookMeetingRooms: boolean;
  canAccessPremiumAreas: boolean;
  isActive: boolean;
  memberType?: MemberType; // Si spécifique à un type
}

export interface CreateMemberRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  memberType: MemberType;
  subscriptionId: string;
  company?: string;
  studentCode?: string;
  iceNumber?: string;
}

export interface UpdateMemberRequest {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  memberType?: MemberType;
  subscriptionId?: string;
  status?: MemberStatus;
  company?: string;
  studentCode?: string;
  iceNumber?: string;
}

// Fonction utilitaire pour obtenir le nom complet
export function getMemberFullName(member: Member): string {
  return `${member.firstName} ${member.lastName}`;
}
