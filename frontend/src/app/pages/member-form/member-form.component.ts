import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

// Ng-Zorro imports
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';

import { MemberService } from '../../services/member.service';
import { SubscriptionService } from '../../services/subscription.service';
import { Member, CreateMemberRequest, UpdateMemberRequest, MemberType, MemberStatus } from '../../models/member.model';
import { SubscriptionPlan, MembershipType } from '../../models/subscription.model';

@Component({
  selector: 'app-member-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzButtonModule,
    NzIconModule,
    NzInputModule,
    NzSelectModule,
    NzFormModule,
    NzCardModule,
    NzSpinModule,
    NzDatePickerModule
  ],
  templateUrl: './member-form.component.html',
  styleUrl: './member-form.component.css'
})
export class MemberFormComponent implements OnInit {

  // Signaux pour la réactivité
  private loadingSignal = signal<boolean>(false);
  private memberSignal = signal<Member | null>(null);

  // Getters pour les templates
  get loading() { return this.loadingSignal(); }
  get member() { return this.memberSignal(); }

  // Propriétés du composant
  isEditMode = false;
  memberId: string | null = null;
  memberForm!: FormGroup;

  // Options pour les selects
  memberTypeOptions = [
    { label: 'Étudiant', value: 'student' },
    { label: 'Professionnel', value: 'professional' },
    { label: 'Freelance', value: 'freelance' }
  ];

  statusOptions = [
    { label: 'Actif', value: 'active' },
    { label: 'Inactif', value: 'inactive' },
    { label: 'Suspendu', value: 'suspended' }
  ];

  subscriptions: SubscriptionPlan[] = [];

  // Enums pour les templates
  MemberType = MemberType;
  MemberStatus = MemberStatus;

  constructor(
    private fb: FormBuilder,
    private memberService: MemberService,
    private subscriptionService: SubscriptionService,
    private router: Router,
    private route: ActivatedRoute,
    private message: NzMessageService
  ) {
    this.initializeForm();
  }

  ngOnInit() {
    // Vérifier si on est en mode édition
    this.memberId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.memberId;

    // Charger les subscriptions
    this.loadSubscriptions();

    if (this.isEditMode && this.memberId) {
      this.loadMember(this.memberId);
    }
  }

  private initializeForm() {
    this.memberForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^[0-9+\-\s()]+$/)]],
      memberType: [null, [Validators.required]],
      subscriptionType: [null, [Validators.required]],
      status: ['active', [Validators.required]],
      company: [''], // Nom de l'entreprise
      studentCode: [''],
      iceNumber: ['']
    });

    // Surveiller les changements de type de membre pour la validation et les abonnements
    this.memberForm.get('memberType')?.valueChanges.subscribe(type => {
      const studentCodeControl = this.memberForm.get('studentCode');
      const iceNumberControl = this.memberForm.get('iceNumber');
      const companyControl = this.memberForm.get('company');
      const subscriptionControl = this.memberForm.get('subscriptionType');

      // Réinitialiser les validations
      studentCodeControl?.clearValidators();
      studentCodeControl?.setValue('');
      iceNumberControl?.clearValidators();
      iceNumberControl?.setValue('');
      companyControl?.clearValidators();
      companyControl?.setValue('');

      // Réinitialiser l'abonnement sélectionné
      subscriptionControl?.setValue('');

      // Appliquer les validations selon le type
      if (type === MemberType.STUDENT) {
        studentCodeControl?.setValidators([Validators.required]);
      } else if (type === MemberType.COMPANY) {
        iceNumberControl?.setValidators([Validators.required]);
        companyControl?.setValidators([Validators.required]);
      }

      studentCodeControl?.updateValueAndValidity();
      iceNumberControl?.updateValueAndValidity();
      companyControl?.updateValueAndValidity();
    });
  }

  private loadMember(id: string) {
    this.loadingSignal.set(true);
    this.memberService.getMemberById(id).subscribe({
      next: (member) => {
        if (member) {
          this.memberSignal.set(member);
          this.populateForm(member);
        } else {
          this.message.error('Membre non trouvé');
          this.router.navigate(['/members']);
        }
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement du membre:', error);
        this.message.error('Erreur lors du chargement du membre');
        this.loadingSignal.set(false);
        this.router.navigate(['/members']);
      }
    });
  }

  private populateForm(member: Member) {
    // Trouver l'ID de l'abonnement basé sur le nom
    const subscription = this.subscriptions.find(s => s.name === member.subscriptionType);
    const subscriptionId = subscription ? subscription.id : '';

    this.memberForm.patchValue({
      firstName: member.firstName,
      lastName: member.lastName,
      email: member.email,
      phone: member.phone,
      memberType: member.memberType,
      subscriptionType: subscriptionId,
      status: member.status,
      company: member.company || '',
      studentCode: member.studentCode || '',
      iceNumber: member.iceNumber || ''
    });
  }

  onSubmit() {
    if (this.memberForm.valid) {
      this.loadingSignal.set(true);

      if (this.isEditMode && this.memberId) {
        this.updateMember();
      } else {
        this.createMember();
      }
    } else {
      this.message.warning('Veuillez remplir tous les champs requis');
      this.markFormGroupTouched();
    }
  }

  private createMember() {
    const formValue = this.memberForm.value;
    const request: CreateMemberRequest = {
      firstName: formValue.firstName,
      lastName: formValue.lastName,
      email: formValue.email,
      phone: formValue.phone,
      memberType: formValue.memberType,
      subscriptionId: formValue.subscriptionType, // Utiliser subscriptionType du form
      company: formValue.memberType === MemberType.COMPANY ? formValue.company : undefined,
      studentCode: formValue.memberType === MemberType.STUDENT ? formValue.studentCode : undefined,
      iceNumber: formValue.memberType === MemberType.COMPANY ? formValue.iceNumber : undefined
    };

    this.memberService.createMember(request).subscribe({
      next: (member) => {
        this.message.success('Membre créé avec succès');
        this.router.navigate(['/members']);
      },
      error: (error) => {
        console.error('Erreur lors de la création:', error);

        // Afficher directement le message d'erreur du backend
        const errorMessage = error?.error?.message || error?.message || 'Erreur lors de la création du membre';
        this.message.error(errorMessage);
        this.loadingSignal.set(false);
      }
    });
  }

  private updateMember() {
    const formValue = this.memberForm.value;
    const request: UpdateMemberRequest = {
      firstName: formValue.firstName,
      lastName: formValue.lastName,
      email: formValue.email,
      phone: formValue.phone,
      memberType: formValue.memberType,
      subscriptionId: formValue.subscriptionType, // Utiliser subscriptionType du form
      status: formValue.status,
      company: formValue.memberType === MemberType.COMPANY ? formValue.company : undefined,
      studentCode: formValue.memberType === MemberType.STUDENT ? formValue.studentCode : undefined,
      iceNumber: formValue.memberType === MemberType.COMPANY ? formValue.iceNumber : undefined
    };

    this.memberService.updateMember(this.memberId!, request).subscribe({
      next: (member) => {
        this.message.success('Membre modifié avec succès');
        this.router.navigate(['/members']);
      },
      error: (error) => {
        console.error('Erreur lors de la modification:', error);

        // Afficher directement le message d'erreur du backend
        const errorMessage = error?.error?.message || error?.message || 'Erreur lors de la modification du membre';
        this.message.error(errorMessage);
        this.loadingSignal.set(false);
      }
    });
  }

  onCancel() {
    this.router.navigate(['/members']);
  }

  private markFormGroupTouched() {
    Object.keys(this.memberForm.controls).forEach(key => {
      const control = this.memberForm.get(key);
      control?.markAsTouched();
    });
  }

  // Méthodes utilitaires pour la validation
  isFieldInvalid(fieldName: string): boolean {
    const field = this.memberForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.memberForm.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} est requis`;
      }
      if (field.errors['email']) {
        return 'Format d\'email invalide';
      }
      if (field.errors['minlength']) {
        return `${this.getFieldLabel(fieldName)} doit contenir au moins ${field.errors['minlength'].requiredLength} caractères`;
      }
      if (field.errors['pattern']) {
        return 'Format invalide';
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      firstName: 'Le prénom',
      lastName: 'Le nom',
      email: 'L\'email',
      phone: 'Le téléphone',
      memberType: 'Le type de membre',
      subscriptionType: 'Le type d\'abonnement',
      status: 'Le statut'
    };
    return labels[fieldName] || fieldName;
  }

  getPageTitle(): string {
    return this.isEditMode ? 'Modifier le membre' : 'Nouveau membre';
  }

  getPageIcon(): string {
    return this.isEditMode ? 'edit' : 'user-add';
  }

  // Charger les subscriptions depuis le backend
  private loadSubscriptions() {
    this.subscriptionService.getPlans().subscribe({
      next: (plans) => {
        this.subscriptions = plans;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des abonnements:', error);
        this.message.error('Erreur lors du chargement des abonnements');
        // Fallback avec des données par défaut en cas d'erreur
        this.subscriptions = [];
      }
    });
  }

  // Filtrer les subscriptions par type de membre
  getSubscriptionsByType(memberType: MemberType | null) {
    if (!memberType) return [];

    // Convertir MemberType vers MembershipType pour la compatibilité
    let membershipType: MembershipType;
    switch (memberType) {
      case MemberType.STUDENT:
        membershipType = MembershipType.STUDENT;
        break;
      case MemberType.PROFESSIONAL:
        membershipType = MembershipType.PROFESSIONAL;
        break;
      case MemberType.COMPANY:
        membershipType = MembershipType.COMPANY;
        break;
      default:
        return [];
    }

    return this.subscriptions.filter(sub =>
      sub.membershipTypes.includes(membershipType) || sub.membershipTypes.length === 0
    );
  }
}
