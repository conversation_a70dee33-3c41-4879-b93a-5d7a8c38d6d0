<div class="member-form-container">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon [nzType]="getPageIcon()" nzTheme="outline"></nz-icon>
        {{ getPageTitle() }}
      </h1>
      <p class="page-description">
        {{ isEditMode ? 'Modifiez les informations du membre' : 'Ajoutez un nouveau membre à votre espace' }}
      </p>
    </div>
    <div class="header-actions">
      <button nz-button nzType="default" (click)="onCancel()" class="cancel-button">
        <nz-icon nzType="arrow-left"></nz-icon>
        Retour
      </button>
    </div>
  </div>

  <!-- Formulaire -->
  <nz-card class="form-card" nzTitle="Informations du membre">
    <nz-spin [nzSpinning]="loading">
      <form nz-form [formGroup]="memberForm" (ngSubmit)="onSubmit()">

        <!-- Informations personnelles -->
        <div class="form-section">
          <h3>Informations personnelles</h3>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Prénom</nz-form-label>
                <nz-form-control [nzErrorTip]="getFieldError('firstName')">
                  <input
                    nz-input
                    formControlName="firstName"
                    placeholder="Prénom du membre"
                    [class.error]="isFieldInvalid('firstName')"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Nom</nz-form-label>
                <nz-form-control [nzErrorTip]="getFieldError('lastName')">
                  <input
                    nz-input
                    formControlName="lastName"
                    placeholder="Nom du membre"
                    [class.error]="isFieldInvalid('lastName')"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Email</nz-form-label>
                <nz-form-control [nzErrorTip]="getFieldError('email')">
                  <input
                    nz-input
                    formControlName="email"
                    placeholder="<EMAIL>"
                    type="email"
                    [class.error]="isFieldInvalid('email')"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Téléphone</nz-form-label>
                <nz-form-control [nzErrorTip]="getFieldError('phone')">
                  <input
                    nz-input
                    formControlName="phone"
                    placeholder="+212 6 12 34 56 78"
                    [class.error]="isFieldInvalid('phone')"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <nz-form-item *ngIf="memberForm.get('memberType')?.value === MemberType.STUDENT">
            <nz-form-label nzRequired>Code étudiant</nz-form-label>
            <nz-form-control nzErrorTip="Le code étudiant est requis">
              <input
                nz-input
                formControlName="studentCode"
                placeholder="ETU2024001"
              />
            </nz-form-control>
          </nz-form-item>

          <div *ngIf="memberForm.get('memberType')?.value === MemberType.COMPANY">
            <nz-form-item>
              <nz-form-label nzRequired>Nom de l'entreprise</nz-form-label>
              <nz-form-control nzErrorTip="Le nom de l'entreprise est requis">
                <input
                  nz-input
                  formControlName="company"
                  placeholder="Nom de l'entreprise"
                />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item>
              <nz-form-label nzRequired>ICE Entreprise</nz-form-label>
              <nz-form-control nzErrorTip="Le numéro ICE est requis">
                <input
                  nz-input
                  formControlName="iceNumber"
                  placeholder="ICE000000001"
                />
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <!-- Informations d'abonnement -->
        <div class="form-section">
          <h3>Informations d'abonnement</h3>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Type de membre</nz-form-label>
                <nz-form-control nzErrorTip="Le type de membre est requis">
                  <nz-select
                    formControlName="memberType"
                    nzPlaceHolder="Sélectionner le type"
                  >
                    <nz-option [nzValue]="MemberType.STUDENT" nzLabel="Étudiant"></nz-option>
                    <nz-option [nzValue]="MemberType.PROFESSIONAL" nzLabel="Professionnel"></nz-option>
                    <nz-option [nzValue]="MemberType.COMPANY" nzLabel="Entreprise"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Abonnement</nz-form-label>
                <nz-form-control nzErrorTip="Veuillez sélectionner un abonnement">
                  <nz-select
                    formControlName="subscriptionType"
                    nzPlaceHolder="Sélectionner un abonnement"
                  >
                    <nz-option
                      *ngFor="let subscription of getSubscriptionsByType(memberForm.get('memberType')?.value)"
                      [nzValue]="subscription.id"
                      [nzLabel]="subscription.name + ' - ' + subscription.price + ' MAD/mois'"
                    ></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div nz-row [nzGutter]="16" *ngIf="isEditMode">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Statut</nz-form-label>
                <nz-form-control>
                  <nz-select formControlName="status" nzPlaceHolder="Sélectionner le statut">
                    <nz-option [nzValue]="MemberStatus.ACTIVE" nzLabel="Actif"></nz-option>
                    <nz-option [nzValue]="MemberStatus.INACTIVE" nzLabel="Inactif"></nz-option>
                    <nz-option [nzValue]="MemberStatus.SUSPENDED" nzLabel="Suspendu"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="form-actions">
          <button nz-button nzType="default" (click)="onCancel()" class="cancel-action">
            Annuler
          </button>
          <button
            nz-button
            nzType="primary"
            [nzLoading]="loading"
            [disabled]="!memberForm.valid"
            class="submit-action"
          >
            {{ isEditMode ? 'Modifier' : 'Créer' }} le membre
          </button>
        </div>
      </form>
    </nz-spin>
  </nz-card>
</div>
